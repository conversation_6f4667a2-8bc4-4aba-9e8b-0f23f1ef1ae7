import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// GetX Controller for managing user profile state across the entire app
class ProfileController extends GetxController {
  static ProfileController get instance => Get.find();

  // Reactive variables for current user profile
  final _currentUserId = ''.obs;
  final _profileImageUrl = ''.obs;
  final _displayName = ''.obs;
  final _username = ''.obs;
  final _bio = ''.obs;
  final _email = ''.obs;
  final _isLoading = false.obs;
  final _postsCount = 0.obs;
  final _followersCount = 0.obs;
  final _followingCount = 0.obs;
  final _walletBalance = 0.0.obs;

  // Cache for other users' profile data
  final _userProfileCache = <String, Map<String, dynamic>>{}.obs;

  // Stats caching
  DateTime? _lastStatsUpdate;
  static const Duration _statsCacheDuration = Duration(minutes: 5);

  // Getters for reactive access
  String get currentUserId => _currentUserId.value;
  String get profileImageUrl => _profileImageUrl.value;
  String get displayName => _displayName.value;
  String get username => _username.value;
  String get bio => _bio.value;
  String get email => _email.value;
  bool get isLoading => _isLoading.value;
  int get postsCount => _postsCount.value;
  int get followersCount => _followersCount.value;
  int get followingCount => _followingCount.value;
  double get walletBalance => _walletBalance.value;

  // Reactive getters for UI binding
  RxString get currentUserIdRx => _currentUserId;
  RxString get profileImageUrlRx => _profileImageUrl;
  RxString get displayNameRx => _displayName;
  RxString get usernameRx => _username;
  RxString get bioRx => _bio;
  RxString get emailRx => _email;
  RxBool get isLoadingRx => _isLoading;
  RxInt get postsCountRx => _postsCount;
  RxInt get followersCountRx => _followersCount;
  RxInt get followingCountRx => _followingCount;
  RxDouble get walletBalanceRx => _walletBalance;

  @override
  void onInit() {
    super.onInit();
    _initializeCurrentUser();
  }

  /// Initialize current user data
  Future<void> _initializeCurrentUser() async {
    try {
      _isLoading.value = true;

      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        _currentUserId.value = user.uid;
        _email.value = user.email ?? '';

        await _loadCurrentUserProfile();
        await _loadCurrentUserStats();
      }
    } catch (e) {
      debugPrint('ProfileController: Error initializing current user: $e');
    } finally {
      _isLoading.value = false;
    }
  }

  /// Load current user's profile data from Firestore
  Future<void> _loadCurrentUserProfile() async {
    try {
      if (_currentUserId.value.isEmpty) return;

      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(_currentUserId.value)
          .get();

      if (userDoc.exists) {
        final userData = userDoc.data()!;
        _updateCurrentUserData(userData);

        // Cache the current user's data
        _userProfileCache[_currentUserId.value] = userData;

        // debugPrint('ProfileController: Loaded current user profile');
      }
    } catch (e) {
      debugPrint('ProfileController: Error loading current user profile: $e');
    }
  }

  /// Update current user reactive variables
  void _updateCurrentUserData(Map<String, dynamic> userData) {
    _profileImageUrl.value =
        userData['profileImageUrl'] ?? userData['photoUrl'] ?? '';
    _displayName.value = userData['name'] ?? userData['displayName'] ?? '';
    _username.value = userData['username'] ?? '';
    _bio.value = userData['bio'] ?? '';
  }

  /// Load current user's stats (posts, followers, following, wallet) with batching and caching
  Future<void> _loadCurrentUserStats() async {
    try {
      if (_currentUserId.value.isEmpty) return;

      // Check if stats are cached and still valid
      if (_lastStatsUpdate != null &&
          DateTime.now().difference(_lastStatsUpdate!) < _statsCacheDuration) {
        // debugPrint('ProfileController: Using cached stats');
        return;
      }

      // Batch all queries to run concurrently for better performance
      final futures = await Future.wait([
        // Load posts count
        FirebaseFirestore.instance
            .collection('posts')
            .where('authorId', isEqualTo: _currentUserId.value)
            .count()
            .get(),

        // Load followers count
        FirebaseFirestore.instance
            .collection('follows')
            .where('followingId', isEqualTo: _currentUserId.value)
            .count()
            .get(),

        // Load following count
        FirebaseFirestore.instance
            .collection('follows')
            .where('followerId', isEqualTo: _currentUserId.value)
            .count()
            .get(),
      ]);

      // Update reactive variables with results
      _postsCount.value = futures[0].count ?? 0;
      _followersCount.value = futures[1].count ?? 0;
      _followingCount.value = futures[2].count ?? 0;

      // Update cache timestamp
      _lastStatsUpdate = DateTime.now();

      // debugPrint(
      //   'ProfileController: Loaded user stats (batched) - Posts: ${_postsCount.value}, Followers: ${_followersCount.value}, Following: ${_followingCount.value}',
      // );
    } catch (e) {
      debugPrint('ProfileController: Error loading user stats: $e');
    }
  }

  /// Update current user's profile data
  Future<void> updateCurrentUserProfile({
    String? profileImageUrl,
    String? displayName,
    String? username,
    String? bio,
  }) async {
    try {
      if (_currentUserId.value.isEmpty) return;

      _isLoading.value = true;

      final updateData = <String, dynamic>{};

      if (profileImageUrl != null) {
        updateData['profileImageUrl'] = profileImageUrl;
        // debugPrint(
        //   'ProfileController: Updating profile image URL from "${_profileImageUrl.value}" to "$profileImageUrl"',
        // );
        _profileImageUrl.value = profileImageUrl;
        // debugPrint(
        //   'ProfileController: Profile image URL updated to "${_profileImageUrl.value}"',
        // );
      }

      if (displayName != null) {
        updateData['name'] = displayName;
        updateData['displayName'] = displayName;
        _displayName.value = displayName;
      }

      if (username != null) {
        updateData['username'] = username;
        _username.value = username;
      }

      if (bio != null) {
        updateData['bio'] = bio;
        _bio.value = bio;
      }

      if (updateData.isNotEmpty) {
        updateData['updatedAt'] = FieldValue.serverTimestamp();

        await FirebaseFirestore.instance
            .collection('users')
            .doc(_currentUserId.value)
            .update(updateData);

        // Update cache
        _userProfileCache[_currentUserId.value] = {
          ..._userProfileCache[_currentUserId.value] ?? {},
          ...updateData,
        };

        // debugPrint('ProfileController: Updated current user profile');
      }
    } catch (e) {
      debugPrint('ProfileController: Error updating current user profile: $e');
      rethrow;
    } finally {
      _isLoading.value = false;
    }
  }

  /// Get profile data for any user (with caching)
  Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    if (userId.isEmpty) return null;

    // Check cache first
    if (_userProfileCache.containsKey(userId)) {
      return _userProfileCache[userId];
    }

    try {
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .get();

      if (userDoc.exists) {
        final userData = userDoc.data()!;
        _userProfileCache[userId] = userData;
        return userData;
      }
    } catch (e) {
      debugPrint(
        'ProfileController: Error getting user profile for $userId: $e',
      );
    }

    return null;
  }

  /// Get profile image URL for any user
  Future<String?> getUserProfileImage(String userId) async {
    final userData = await getUserProfile(userId);
    return userData?['profileImageUrl'] ?? userData?['photoUrl'];
  }

  /// Get display name for any user
  Future<String> getUserDisplayName(String userId) async {
    final userData = await getUserProfile(userId);
    return userData?['name'] ??
        userData?['displayName'] ??
        userData?['username'] ??
        'Unknown User';
  }

  /// Get username for any user
  Future<String> getUserUsername(String userId) async {
    final userData = await getUserProfile(userId);
    return userData?['username'] ?? '';
  }

  /// Clear cache for a specific user (useful when user updates profile)
  void clearUserCache(String userId) {
    _userProfileCache.remove(userId);
  }

  /// Clear all cached user data
  void clearAllCache() {
    _userProfileCache.clear();
  }

  /// Refresh current user data
  Future<void> refreshCurrentUser() async {
    await _loadCurrentUserProfile();
    await _loadCurrentUserStats();
  }

  /// Initialize current user (public method for login/auth flows)
  Future<void> initializeCurrentUser() async {
    await _initializeCurrentUser();
  }

  /// Update wallet balance
  void updateWalletBalance(double balance) {
    _walletBalance.value = balance;
  }

  /// Update stats counts
  void updateStats({
    int? postsCount,
    int? followersCount,
    int? followingCount,
  }) {
    if (postsCount != null) _postsCount.value = postsCount;
    if (followersCount != null) _followersCount.value = followersCount;
    if (followingCount != null) _followingCount.value = followingCount;
  }

  /// Check if current user data is available
  bool get hasCurrentUserData =>
      _currentUserId.value.isNotEmpty && _username.value.isNotEmpty;

  /// Get current user's initials for avatar fallback
  String get currentUserInitials {
    if (_displayName.value.isNotEmpty) {
      return _displayName.value
          .split(' ')
          .map((e) => e[0])
          .take(2)
          .join()
          .toUpperCase();
    } else if (_username.value.isNotEmpty) {
      return _username.value.substring(0, 1).toUpperCase();
    }
    return 'U';
  }

  /// Invalidate stats cache to force refresh on next load
  void invalidateStatsCache() {
    _lastStatsUpdate = null;
    // debugPrint('ProfileController: Stats cache invalidated');
  }

  /// Force refresh stats (ignores cache)
  Future<void> refreshStats() async {
    invalidateStatsCache();
    await _loadCurrentUserStats();
  }
}
