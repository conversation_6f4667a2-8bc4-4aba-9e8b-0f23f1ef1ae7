import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/post_service.dart';

/// GetX Controller for managing poll state and voting logic
class PollController extends GetxController {
  static PollController get instance => Get.find();

  final PostService _postService = PostService();

  // Reactive variables for poll state
  final _votingStates = <String, bool>{}.obs; // postId -> isVoting
  final _optimisticVotes = <String, Map<String, int>>{}.obs; // postId -> votes
  final _userVotes = <String, String?>{}.obs; // postId -> userVote
  final _pendingVotes =
      <String, String?>{}.obs; // postId -> pending vote option

  // Reactive getters
  bool isVoting(String postId) => _votingStates[postId] ?? false;
  Map<String, int>? getOptimisticVotes(String postId) =>
      _optimisticVotes[postId];
  String? getUserVote(String postId) => _userVotes[postId];

  // Safe getters that ensure initialization
  int getVoteCount(String postId, String option) {
    final votes = _optimisticVotes[postId];
    if (votes == null) return 0;
    return votes[option] ?? 0;
  }

  int getTotalVotes(String postId) {
    final votes = _optimisticVotes[postId];
    if (votes == null) return 0;
    return votes.values.fold(0, (total, voteCount) => total + voteCount);
  }

  double getVotePercentage(String postId, String option) {
    final totalVotes = getTotalVotes(postId);
    if (totalVotes == 0) return 0.0;
    final optionVotes = getVoteCount(postId, option);
    return (optionVotes / totalVotes) * 100;
  }

  @override
  void onInit() {
    super.onInit();
    // Don't auto-initialize - wait for explicit initialization after authentication
    debugPrint(
        'PollController: Created but not initialized - waiting for authentication');
  }

  /// Initialize user votes from current posts with authentication check
  void _initializeUserVotes() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null || !currentUser.emailVerified) {
      debugPrint(
          'PollController: User not authenticated, skipping initialization');
      return;
    }

    debugPrint(
        'PollController: Initializing for authenticated user: ${currentUser.uid}');
    // We'll initialize poll data when posts are loaded through updatePollData
    // This avoids calling getAllPosts which might not exist or be synchronous
  }

  /// Initialize poll controller after authentication
  void initializeWithAuth() {
    _initializeUserVotes();
  }

  /// Update poll data when posts are updated
  void updatePollData(Post post) {
    if (!post.hasPoll) return;

    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser != null) {
      final userVoteFromPost = post.getUserVote(currentUser.uid);
      final currentUserVote = _userVotes[post.id];
      final pendingVote = _pendingVotes[post.id];

      // Handle successful vote confirmation from real-time updates
      if (pendingVote != null && userVoteFromPost == pendingVote) {
        // Vote was successful - clear pending state and update user vote
        _pendingVotes[post.id] = null;
        _userVotes[post.id] = userVoteFromPost;
        _votingStates[post.id] = false;

        // Show success message
        Get.snackbar(
          'Vote Recorded',
          'Your vote has been successfully recorded!',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: const Color(0xFF4CAF50), // Green color
          colorText: const Color(0xFFFFFFFF), // White color
          duration: const Duration(seconds: 2),
        );

        debugPrint('Vote confirmed for post ${post.id}, option $pendingVote');
      }
      // If the post shows the user has voted, but our controller doesn't know about it,
      // update our state (handles cases where vote was made elsewhere)
      else if (userVoteFromPost != null &&
          currentUserVote != userVoteFromPost) {
        _userVotes[post.id] = userVoteFromPost;
        debugPrint(
            'Updated user vote from real-time data for post ${post.id}: $userVoteFromPost');
      }
      // If post shows no vote but we think user voted, clear our state
      else if (userVoteFromPost == null &&
          currentUserVote != null &&
          pendingVote == null) {
        _userVotes[post.id] = null;
        debugPrint('Cleared user vote from real-time data for post ${post.id}');
      }

      // Also check the voter collection for more accurate data
      _checkVoterCollection(post.id, currentUser.uid);

      // Debug logging
      debugPrint('Poll ${post.id}: User vote from post = $userVoteFromPost');
      debugPrint(
          'Poll ${post.id}: User vote in controller = ${_userVotes[post.id]}');
      debugPrint('Poll ${post.id}: Has voted = ${_userVotes[post.id] != null}');
    }

    // Always update vote counts from real-time data unless we're actively voting
    // This ensures the UI shows the most current vote counts
    if (!isVoting(post.id)) {
      final newVotes = Map<String, int>.from(post.pollVotes);
      final currentVotes = _optimisticVotes[post.id];

      // Only update if votes actually changed to avoid unnecessary rebuilds
      if (currentVotes == null || !_mapsEqual(currentVotes, newVotes)) {
        _optimisticVotes[post.id] = newVotes;
        debugPrint('Updated vote counts for post ${post.id}: $newVotes');
      }
    }

    // Don't call update() during build - let GetBuilder handle it
  }

  /// Check the voter collection for user's vote
  Future<void> _checkVoterCollection(String postId, String userId) async {
    try {
      final voterDoc = FirebaseFirestore.instance
          .collection('poll_voters')
          .doc('${postId}_$userId');
      final snapshot = await voterDoc.get();

      if (snapshot.exists) {
        final data = snapshot.data()!;
        final option = data['option'] as String?;
        if (option != null && _userVotes[postId] != option) {
          _userVotes[postId] = option;
          debugPrint(
              'Updated user vote from voter collection for post $postId: $option');
        }
      } else if (_userVotes[postId] != null) {
        // User vote not found in collection, clear local state
        _userVotes[postId] = null;
        debugPrint(
            'Cleared user vote for post $postId - not found in voter collection');
      }
    } catch (e) {
      debugPrint('Error checking voter collection: $e');
    }
  }

  /// Helper to compare two maps for equality
  bool _mapsEqual<K, V>(Map<K, V> map1, Map<K, V> map2) {
    if (map1.length != map2.length) return false;
    for (final key in map1.keys) {
      if (map1[key] != map2[key]) return false;
    }
    return true;
  }

  /// Vote on a poll with optimistic updates (one-time only)
  void voteOnPoll(String postId, String option) {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null || isVoting(postId)) return;

    // Check if user has already voted - prevent re-voting
    if (hasUserVoted(postId)) {
      Get.snackbar(
        'Already Voted',
        'You have already voted on this poll. You can only vote once.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: const Color(0xFFFF9800), // Orange color
        colorText: const Color(0xFFFFFFFF), // White color
        duration: const Duration(seconds: 2),
      );
      return;
    }

    // Immediately record the vote optimistically
    _userVotes[postId] = option;
    _votingStates[postId] = true;
    _pendingVotes[postId] = option; // Track pending vote

    // Update vote counts optimistically
    final currentVotes = _optimisticVotes[postId] ?? <String, int>{};
    final newVotes = Map<String, int>.from(currentVotes);
    newVotes[option] = (newVotes[option] ?? 0) + 1;
    _optimisticVotes[postId] = newVotes;

    // Trigger UI update for this specific poll
    update(['poll_$postId']);

    // Perform actual vote in background using a simple approach
    _performBackgroundVote(postId, option);
  }

  /// Perform the actual vote in background without Future complications
  void _performBackgroundVote(String postId, String option) {
    // Use Future.microtask to avoid Timer issues
    Future.microtask(() async {
      try {
        // Simple direct call without complex error handling
        await _postService.voteOnPoll(postId, option);

        // Vote successful - the real-time listener will update the data
        debugPrint('Vote successful for post $postId, option $option');

        // Don't show success message immediately - wait for real-time confirmation
      } catch (e, stackTrace) {
        // Vote failed - rollback optimistic changes
        debugPrint('Poll voting error: $e');
        debugPrint('Stack trace: $stackTrace');

        // Try to extract the actual error message
        String actualError = e.toString();
        if (e is Exception) {
          actualError = e.toString();
        }

        // Remove the optimistic vote
        _userVotes[postId] = null;
        _pendingVotes[postId] = null; // Clear pending vote

        // Rollback the optimistic vote count
        final currentVotes = _optimisticVotes[postId] ?? <String, int>{};
        final newVotes = Map<String, int>.from(currentVotes);
        final currentCount = newVotes[option] ?? 0;
        if (currentCount > 0) {
          newVotes[option] = currentCount - 1;
          if (newVotes[option]! <= 0) {
            newVotes[option] = 0; // Keep the option but set to 0
          }
        }
        _optimisticVotes[postId] = newVotes;

        update(['poll_$postId']);

        // Show more specific error message
        String errorMessage = 'Failed to record your vote. Please try again.';
        if (actualError.contains('already voted')) {
          errorMessage = 'You have already voted on this poll.';
        } else if (actualError.contains('permission')) {
          errorMessage = 'Permission denied. Please check your account.';
        } else if (actualError.contains('network') ||
            actualError.contains('timeout')) {
          errorMessage = 'Network error. Please check your connection.';
        }

        Get.snackbar(
          'Vote Failed',
          errorMessage,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: const Color(0xFFD32F2F),
          colorText: const Color(0xFFFFFFFF),
          duration: const Duration(seconds: 3),
        );
      } finally {
        _votingStates[postId] = false;
        update(['poll_$postId']);
      }
    });
  }

  /// Check if user has voted on a poll (reactive)
  bool hasUserVoted(String postId) {
    return _userVotes[postId] != null;
  }

  /// Clear poll data for a specific post
  void clearPollData(String postId) {
    _votingStates.remove(postId);
    _optimisticVotes.remove(postId);
    _userVotes.remove(postId);
  }

  /// Clear all poll data
  void clearAllPollData() {
    _votingStates.clear();
    _optimisticVotes.clear();
    _userVotes.clear();
  }

  @override
  void onClose() {
    clearAllPollData();
    super.onClose();
  }
}
