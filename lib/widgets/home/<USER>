import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:money_mouthy_two/controllers/wallet_controller.dart';
import 'package:money_mouthy_two/controllers/post_controller.dart';
import 'package:money_mouthy_two/services/post_service.dart';
import 'package:money_mouthy_two/widgets/post_card.dart';
import 'package:money_mouthy_two/widgets/skeleton_loader.dart';
import 'package:money_mouthy_two/screens/post_detail_screen.dart';

/// Posts Feed Widget - Optimized with minimal rebuilds
class PostsFeed extends StatefulWidget {
  final String category;

  const PostsFeed({super.key, required this.category});

  @override
  State<PostsFeed> createState() => _PostsFeedState();
}

class _PostsFeedState extends State<PostsFeed>
    with AutomaticKeepAliveClientMixin {
  final WalletController walletController = Get.find<WalletController>();
  final ScrollController _scrollController = ScrollController();

  // Cache the filtered posts to prevent recalculation
  List<Post> _cachedPosts = [];
  String _lastCategory = '';

  // Real-time scroll position management
  double _lastScrollPosition = 0.0;
  int _lastPostCount = 0;

  // Cache invalidation tracking
  int _cacheVersion = 0;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _setupScrollListener();
  }

  @override
  void didUpdateWidget(PostsFeed oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Force cache invalidation when category changes
    if (oldWidget.category != widget.category) {
      _invalidateCache();
    }
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 200) {
        _loadMorePosts();
      }
    });
  }

  Future<void> _loadMorePosts() async {
    // Use hybrid pagination approach - load historical posts
    final controller = Get.find<PostController>();
    final success = await controller.loadMoreHistoricalPosts(
      category: widget.category == 'All' ? null : widget.category,
    );

    // if (success) {
    //   debugPrint('PostsFeed: Successfully loaded more historical posts');
    // } else {
    //   debugPrint('PostsFeed: No more historical posts available');
    // }
  }

  /// Invalidate cache to force refresh
  void _invalidateCache() {
    setState(() {
      _cacheVersion++;
      _cachedPosts.clear();
      _lastCategory = '';
      _lastPostCount = 0;
    });
    // debugPrint('PostsFeed: Cache invalidated for category ${widget.category}');
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    // Only log when category changes to reduce noise
    if (_lastCategory != widget.category) {
      // debugPrint('PostsFeed: Building widget for category ${widget.category}');
      _lastCategory = widget.category;
    }

    // Check if PostController is initialized before trying to access it
    if (!Get.isRegistered<PostController>()) {
      return ListView.builder(
        padding: const EdgeInsets.fromLTRB(12, 4, 12, 80),
        itemCount: 5,
        itemBuilder: (context, index) => const PostCardSkeleton(),
      );
    }

    return GetBuilder<PostController>(
      id: 'posts_${widget.category} ',
      builder: (controller) {
        if (controller.isLoading) {
          return ListView.builder(
            padding: const EdgeInsets.fromLTRB(12, 4, 12, 80),
            itemCount: 5,
            itemBuilder: (context, index) => const PostCardSkeleton(),
          );
        }

        // Smart caching with real-time update detection
        final allPosts = controller.getFilteredPosts(category: widget.category);
        final topPost = controller.getTopPaidPostForCategory(widget.category);

        // Detect if posts have changed (new posts added)
        final newPostCount = allPosts.length;
        final hasNewPosts = newPostCount > _lastPostCount;

        // Check if cache needs to be updated
        final needsCacheUpdate = _lastCategory != widget.category ||
            _cachedPosts.isEmpty ||
            hasNewPosts;

        if (needsCacheUpdate) {
          // Store current scroll position before updating
          if (hasNewPosts && _scrollController.hasClients) {
            _lastScrollPosition = _scrollController.offset;
          }

          _cachedPosts = List<Post>.from(allPosts);

          // Remove the top paid post from regular feed to avoid duplication
          if (topPost != null) {
            _cachedPosts.removeWhere((post) => post.id == topPost.id);
          }

          _lastCategory = widget.category;
          _lastPostCount = newPostCount;

          // debugPrint(
          //   'PostsFeed: Updated cache with ${_cachedPosts.length} posts for category ${widget.category}${hasNewPosts ? ' (new posts detected)' : ''}${_lastCategory != widget.category ? ' (category changed)' : ''}',
          // );

          // Maintain scroll position after new posts are added
          if (hasNewPosts &&
              _scrollController.hasClients &&
              _lastScrollPosition > 0) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (_scrollController.hasClients) {
                _scrollController.animateTo(
                  _lastScrollPosition,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                );
              }
            });
          }
        }

        if (_cachedPosts.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.post_add, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'No posts in ${widget.category}',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Be the first to share something!',
                  style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            HapticFeedback.mediumImpact();
            _cachedPosts.clear(); // Clear cache on refresh
            await controller.refreshPosts();
          },
          color: const Color(0xFF4285F4),
          backgroundColor: Colors.white,
          strokeWidth: 3,
          displacement: 60,
          child: ListView.builder(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(
              parent: AlwaysScrollableScrollPhysics(),
            ),
            padding: const EdgeInsets.fromLTRB(12, 4, 12, 80),
            itemCount: _cachedPosts.length,
            itemBuilder: (context, index) {
              return PostCard(
                key: ValueKey(
                    _cachedPosts[index].id), // Prevent unnecessary rebuilds
                post: _cachedPosts[index],
                onLike: () => _handleLike(_cachedPosts[index]),
                onView: () => _handleView(_cachedPosts[index]),
                onTap: () => _handlePostTap(_cachedPosts[index]),
              );
            },
          ),
        );
      },
    );
  }

  void _handleLike(Post post) {
    // Use PostService for individual post actions
    PostService().likePost(post.id);
  }

  void _handleView(Post post) {
    // Use PostService for individual post actions (views don't need reactive updates)
    PostService().viewPost(post.id);
  }

  void _handlePostTap(Post post) {
    // Navigate to post detail view
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => PostDetailScreen(post: post)),
    );
  }
}
