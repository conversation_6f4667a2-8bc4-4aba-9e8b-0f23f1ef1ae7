import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:money_mouthy_two/screens/create_account.dart';
import 'package:money_mouthy_two/screens/login.dart';
import 'package:money_mouthy_two/widgets/app_logo.dart';

class SignUpScreen extends StatelessWidget {
  const SignUpScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: BoxConstraints(
              maxWidth: kIsWeb ? 800 : double.infinity,
            ),
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Column(
                children: [
                  const SizedBox(height: 80),

                  // Logo and app name
                  const Center(child: AppLogo()),

                  const SizedBox(height: 60),

                  // Your opinion has minimum value tagline
                  RichText(
                    text: const TextSpan(
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      children: <TextSpan>[
                        TextSpan(text: 'Your opinion has minimum value '),
                        TextSpan(
                          text: '\$0.05',
                          style: TextStyle(
                            color: Color(0xFF5159FF),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        TextSpan(text: 'or more! Join Now!'),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 40),

                  // Social login buttons
                  // Column(
                  //   children: [
                  //     // Google login button
                  //     ElevatedButton(
                  //       onPressed: () {},
                  //       style: ElevatedButton.styleFrom(
                  //         backgroundColor: Colors.white,
                  //         foregroundColor: Colors.black,
                  //         elevation: 0,
                  //         side: BorderSide(
                  //           color: Colors.grey.shade300,
                  //           width: 1,
                  //         ),
                  //         shape: RoundedRectangleBorder(
                  //           borderRadius: BorderRadius.circular(12),
                  //         ),
                  //         padding: const EdgeInsets.symmetric(vertical: 16),
                  //         minimumSize: const Size(double.infinity, 56),
                  //       ),
                  //       child: Row(
                  //         mainAxisAlignment: MainAxisAlignment.center,
                  //         children: [
                  //           // Google logo placeholder
                  //           SizedBox(
                  //             width: 24,
                  //             height: 24,
                  //             child: ClipOval(
                  //               child: Image.asset(
                  //                 'assets/images/google.png',
                  //                 fit: BoxFit.cover,
                  //               ),
                  //             ),
                  //           ),
                  //           const SizedBox(width: 12),
                  //           const Text(
                  //             "Continue with Google",
                  //             style: TextStyle(
                  //               fontSize: 16,
                  //               fontWeight: FontWeight.w500,
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //     ),

                  //     const SizedBox(height: 16),

                  //     // Apple login button
                  //     ElevatedButton(
                  //       onPressed: () {},
                  //       style: ElevatedButton.styleFrom(
                  //         backgroundColor: Colors.white,
                  //         foregroundColor: Colors.black,
                  //         elevation: 0,
                  //         side: BorderSide(
                  //           color: Colors.grey.shade300,
                  //           width: 1,
                  //         ),
                  //         shape: RoundedRectangleBorder(
                  //           borderRadius: BorderRadius.circular(12),
                  //         ),
                  //         padding: const EdgeInsets.symmetric(vertical: 16),
                  //         minimumSize: const Size(double.infinity, 56),
                  //       ),
                  //       child: Row(
                  //         mainAxisAlignment: MainAxisAlignment.center,
                  //         children: [
                  //           const Icon(Icons.apple, size: 24),
                  //           const SizedBox(width: 12),
                  //           const Text(
                  //             "Continue with Apple",
                  //             style: TextStyle(
                  //               fontSize: 16,
                  //               fontWeight: FontWeight.w500,
                  //             ),
                  //           ),
                  //         ],
                  //       ),
                  //     ),
                  //   ],
                  // ),

                  // const SizedBox(height: 32),

                  // Create account button
                  ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const CreateAccountScreen(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF5159FF),
                      foregroundColor: Colors.white,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      minimumSize: const Size(double.infinity, 56),
                    ),
                    child: const Text(
                      "Create an account",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),

                  const SizedBox(height: 40),

                  // Terms and conditions
                  Padding(
                    padding: const EdgeInsets.only(bottom: 40),
                    child: RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                        children: [
                          const TextSpan(text: "\nHave an account already? "),
                          TextSpan(
                            text: "LOGIN",
                            style: const TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const LoginScreen(),
                                  ),
                                );
                              },
                          ),
                          const TextSpan(
                            text: "By signing up you agree to Money Mouthy's ",
                          ),
                          TextSpan(
                            text: "terms and conditions",
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Colors.black,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                Navigator.pushNamed(context, '/terms');
                              },
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
